x-common-settings: &common-settings
  image: kontent
  build:
    context: ./kontent
    dockerfile: Dockerfile
  volumes:
    - ./kontent/:/app/
  env_file:
    - ./.env

services:
  web:
    <<: *common-settings
    ports:
      - "8000:8000"
    command: bash -c "gunicorn --reload --config gunicorn_config.py config.wsgi --bind 0.0.0.0:8000"
    depends_on:
      - db

  worker:
    <<: *common-settings
    command: /usr/bin/supervisord -c /etc/supervisord.conf
    depends_on:
      - rabbitmq
      - db

  rabbitmq:
    image: rabbitmq:3-management-alpine
    ports:
      - "5672:5672"
      - "15672:15672"
    environment:
      - RABBITMQ_DEFAULT_USER=admin
      - RABBITMQ_DEFAULT_PASS=admin

  db:
    image: postgres:13.0-alpine
    volumes:
      - postgres_data:/var/lib/postgresql/data/
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=kontent_dev_db
    ports:
      - "54322:5432"

volumes:
  postgres_data:
