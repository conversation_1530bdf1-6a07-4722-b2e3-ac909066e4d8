import uuid
from datetime import datetime, timed<PERSON>ta
from random import randint
from elasticsearch import Elasticsearch

es = Elasticsearch(
    ['http://elastic.keepsdev.com:80'],
    basic_auth=('keepsdev', '7scCFFpYNBAR'),
)

INDEX = 'analytics-test'
es.indices.create(index=INDEX, ignore=400)

COMPANIES = [('cc615838-288e-40e3-a97f-dd48623b8b58', 'Blug Co'),
             ('1e3d25c3-3d5e-4b69-b088-22d6b3a4ecd6', 'M Republic Relations S.A'),
             ('fa9e0335-3a83-42ff-8e93-a198b8ac01a3', 'OMG Consulting', 'Reputation'),
             ('0b8397bd-5fd8-4f83-a0c8-b0f28f081b7a', 'Add Solutions'),
             ('eebba29a-e27f-4e4d-adc8-1a9e8638f478', 'Co<PERSON>ute<PERSON>'),
             ('3470fefe-4094-4b34-b93d-2d6dd00cc776', '<PERSON><PERSON> do <PERSON>'),
             ('1fac0fcf-b8e2-4953-948c-a0a66ab5b3d1', 'Mydas Tecnologia'),
             ('a80fc168-33d2-48de-a57f-def16e4be37b', 'Zatos Soluções em Tecnologia'),
             ('c382cd7d-90dc-4612-a015-dca10715bc9c', 'Vitech Informática'),
             ('5f417702-3cfb-4225-9484-7623acfd31e4', 'Keeps Tecnologia')]

USERS = [('cc615838-288e-40e3-a97f-dd48623b8b58', 'Rangel'),
         ('1e3d25c3-3d5e-4b69-b088-22d6b3a4ecd6', 'Gustavo'),
         ('fa9e0335-3a83-42ff-8e93-a198b8ac01a3', 'Elen'),
         ('0b8397bd-5fd8-4f83-a0c8-b0f28f081b7a', 'Alair'),
         ('eebba29a-e27f-4e4d-adc8-1a9e8638f478', 'Eduarda'),
         ('3470fefe-4094-4b34-b93d-2d6dd00cc776', 'Renato'),
         ('1fac0fcf-b8e2-4953-948c-a0a66ab5b3d1', 'Arthur'),
         ('a80fc168-33d2-48de-a57f-def16e4be37b', 'Pedro'),
         ('c382cd7d-90dc-4612-a015-dca10715bc9c', 'Diego'),
         ('5f417702-3cfb-4225-9484-7623acfd31e4', 'Aldo')]


def category():
    data = [('a80fc168-33d2-48de-a57f-def16e4be37b', 'Lideranca'),
            ('c382cd7d-90dc-4612-a015-dca10715bc9c', 'Desenvolvimento'),
            ('5f417702-3cfb-4225-9484-7623acfd31e4', 'Gestao de Projetos')]

    rand = randint(0, 2)

    return {
        "id": data[rand][0],
        "name": data[rand][1]
    }


def type_course():
    data = [('a80fc168-33d2-48de-a57f-def16e4be37b', 'Public'),
            ('c382cd7d-90dc-4612-a015-dca10715bc9c', 'Open Company'),
            ('5f417702-3cfb-4225-9484-7623acfd31e4', 'Close Company')]

    rand = randint(0, 2)

    return {
        "id": data[rand][0],
        "name": data[rand][1]
    }


def user_creator():
    rand = randint(0, 9)

    return {
        "id": USERS[rand][0],
        "name": USERS[rand][1]
    }


def company_owner():
    rand = randint(0, 9)

    return {
        "id": COMPANIES[rand][0],
        "name": COMPANIES[rand][1]
    }


def company_paid():
    data = []
    rand_top = randint(0, 5)

    for x in range(rand_top):
        rand = randint(0, 9)
        data.append({
            "id": COMPANIES[rand][0],
            "name": COMPANIES[rand][1]
        })

    return data


def bookmarks():
    data = []
    rand_top = randint(0, 9)

    for x in range(rand_top):
        rand = randint(0, 9)
        data.append({
            "id": str(uuid.uuid4()),
            "date": str(datetime.now()),
            "user": {
                "id": USERS[rand][0],
                "name": USERS[rand][1]
            }
        })

    return data


def comments():
    data = []
    rand_top = randint(0, 9)

    for x in range(rand_top):
        rand = randint(0, 9)
        data.append({
            "id": str(uuid.uuid4()),
            "date": str(datetime.now()),
            "comment": "Em minha opinião este curso é de fundamental necessidade para o profissional que quer crescer e mudar a "
                       "visão de processos da empresa em que trabalha.",
            "user": {
                "id": USERS[rand][0],
                "name": USERS[rand][1]
            }
        })

    return data


def ratings():
    data = []
    rand_top = randint(0, 9)

    for x in range(rand_top):
        rand = randint(0, 9)
        data.append({
            "id": str(uuid.uuid4()),
            "date": str(datetime.now()),
            "rating": randint(1, 5),
            "user": {
                "id": USERS[rand][0],
                "name": USERS[rand][1]
            }
        })

    return data


def lectures():
    data = []
    rand_top = randint(0, 15)

    content_types = [
        {
            "id": "799f766c-a956-4c03-b5aa-bde9ba357de8",
            "name": "Podcast",
        },
        {
            "id": "569cc389-ac1d-4fa0-9692-f715b475b59b",
            "name": "Video",
        },
        {
            "id": "2284bfce-fdfc-4477-9143-39c380cc653c",
            "name": "Image",
        },
        {
            "id": "b7094e27-b263-4fed-a928-6f0a78439cbe",
            "name": "Text",
        },
        {
            "id": "7ee375e4-b781-46e6-b0de-0323ebb94b96",
            "name": "Presentation",
        },
        {
            "id": "673e4c02-ae1c-4e61-830b-706d35bd0b11",
            "name": "Spreadsheet",
        },
        {
            "id": "7a41a8e0-ee37-4d0b-ad4f-35bada67134d",
            "name": "Question",
        },
        {
            "id": "e459d983-5c61-4e7d-8310-1eec2dc8f369",
            "name": "Blog",
        },
        {
            "id": "0faac34b-2393-4352-8a94-a9ee0659f824",
            "name": "PDF",
        }
    ]

    for x in range(rand_top):
        rand1 = randint(0, 8)
        rand2 = randint(0, 8)
        rand3 = randint(0, 8)

        data.append({
            "id": str(uuid.uuid4()),
            "name": "Aula {}".format(str(uuid.uuid4())),
            "contents": [{
                "id": str(uuid.uuid4()),
                "url": "https://{}.com".format(str(uuid.uuid4())),
                "type": {
                    "id": content_types[rand1]['id'],
                    "name": content_types[rand1]['name']
                }}, {
                "id": str(uuid.uuid4()),
                "url": "https://{}.com".format(str(uuid.uuid4())),
                "type": {
                    "id": content_types[rand2]['id'],
                    "name": content_types[rand2]['name']
                }}, {
                "id": str(uuid.uuid4()),
                "url": "https://{}.com".format(str(uuid.uuid4())),
                "type": {
                    "id": content_types[rand3]['id'],
                    "name": content_types[rand3]['name']
                }}

            ]})

    return data


def enrolments():
    data = []
    rand_top = randint(0, 9)
    status = ["COMPLETED", "STARTED"]

    for x in range(rand_top):
        rand = randint(0, 9)
        data.append({
            "id": str(uuid.uuid4()),
            "points": 3,
            "start_date": str(datetime.now()),
            "end_date": str(datetime.now()),
            "goal_date": str(datetime.now()),
            "give_up": "FALSE",
            "give_up_comment": "",
            "status": status[randint(0, 1)],
            "user": {
                "id": USERS[rand][0],
                "name": USERS[rand][1]
            }
        })

    return data


def tags():
    data = []
    rand_top = randint(0, 9)

    _tags = [
        ('dev', 1),
        ('python', 0.5),
        ('leadership', 0.8),
        ('education', 0.2),
        ('projects', 0.9),
        ('startup', 0.4)
    ]

    for x in range(rand_top):
        rand = randint(0, 5)
        data.append({
            "name": _tags[rand][0],
            "relevance": _tags[rand][1]
        })

    return data


doc = {
    "id": 123,
    "name": "Curso nome {}".format(str(uuid.uuid4())),
    "description": "descricao do curso",
    "holder_image": "http://{}".format(str(uuid.uuid4())),
    "duration_time": randint(1000, 5000),
    "points": randint(100, 1000),
    "is_active": "TRUE",
    "platform": "Konquest",
    "created_date": datetime.now() - timedelta(randint(2, 100)),
    "category": category(),
    "type": type_course(),
    "user_creator": user_creator(),
    "company_owner": company_owner(),
    "company_paid": company_paid(),
    "bookmarks": bookmarks(),
    "comments": comments(),
    "ratings": ratings(),
    "lectures": lectures(),
    "enrolments": enrolments(),
    "tags": tags()
}

for x in range(10):
    res = es.index(index=INDEX, body=doc)
    es.indices.refresh(index=INDEX)
