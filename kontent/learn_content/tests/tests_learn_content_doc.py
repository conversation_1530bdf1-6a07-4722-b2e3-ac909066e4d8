import uuid

import mock
from django.test import TestCase
from learn_content.models import LearnContentDocument


class ConnectionStub:
    @staticmethod
    def update(index:str, body, refresh, **kwargs):
        return {"result": {}}

class TestsLearnContentDocument(TestCase):
    @mock.patch.object(LearnContentDocument, "_get_connection")
    @mock.patch.object(ConnectionStub, "update")
    def test_should_update_document(self, update, get_connection):
        get_connection.return_value = ConnectionStub()
        document = LearnContentDocument(meta={'id': uuid.uuid4()}, name="name")

        document.update(name="updated name")

        update.assert_called_once()
