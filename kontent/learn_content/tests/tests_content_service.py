import tempfile
import uuid
from typing import List, Optional

import mock
from PIL import Image
from django.test import TestCase
from model_mommy import mommy
import re

from config import settings
from learn_content.models import LearnContent, ContentType, LearnContentDocument, ContentPointRule
from learn_content.services.content_service import ContentService


class ElasticSearchStub:
    def __init__(self, mocked_documents: Optional[List[LearnContentDocument]]):
        self.documents: List[LearnContentDocument]  = mocked_documents

    def save(self, document: LearnContentDocument):
        for position, saved_document in enumerate(self.documents):
            if saved_document.meta["id"] == document.meta["id"]:
                self.documents[position] = document
        self.documents.append(document)

    def search(self, *args, **kwargs):
        result = {"hits": { "hits": [] }}
        for document in self.documents:
            result["hits"]["hits"].append({"_source": document.__dict__["_d_"], "_id": document.meta["id"]})
        return result


class TestsContentService(TestCase):
    def setUp(self) -> None:
        self.s3_url = '{}/{}'.format(settings.AWS_BASE_S3_URL, settings.AWS_BUCKET_NAME)
        self.elastic_search = ElasticSearchStub([])
        mommy.make(ContentType, extensions='pdf png')
        self._service = ContentService(self.elastic_search)

    @staticmethod
    def _learn_document_to_document(
        instance: LearnContent,
        tags: Optional[List[dict]] = None,
        summary: Optional[str] = None,
        language: Optional[str] = None,
        entities: Optional[List[dict]] = None,
        transcript: Optional[str] = None,
        duration: Optional[int] = 10,
        points: Optional[int] = 10
    ):
        document = LearnContentDocument(
            meta={'id': instance.id},
            name=instance.name,
            description=instance.description,
            category=instance.category.id if instance.category else None,
            content_type={
                "id": instance.content_type.id if instance.content_type else None,
                "name": instance.content_type.name if instance.content_type else None,
                "image": instance.content_type.image if instance.content_type else None,
                "image_cover": instance.content_type.image_cover if instance.content_type else None,
            },
            url=instance.url,
            analyzed=instance.analyzed,
            content_transcript=transcript,
            duration=duration,
            points=points,
            tags=tags,
            summary=summary,
            entities=entities,
            language=language,
            created_date=instance.created_date,
            updated_date=instance.updated_date
        )
        return document

    def test_should_get_contents_saved_in_elastic_search_and_relational_database(self):
        self.elastic_search = ElasticSearchStub([])
        content_type = mommy.make(ContentType, extensions='.pdf,.xlsx')
        content_saved_only_in_main_database = mommy.make(LearnContent, content_type=content_type)
        content_saved_in_elastic_search = mommy.make(LearnContent, content_type=content_type, url="url.com")

        self._service = ContentService(self.elastic_search, 20, 15)

        tags = [{"text": "texto texto ateoo", "relevance": 0.9900525212287903}]
        summary = "summary"
        language = "pt-BR"
        entities = None
        transcript = "transcript"
        self.elastic_search.save(self._learn_document_to_document(
            content_saved_in_elastic_search,
            tags=tags,
            summary=summary,
            language=language,
            entities=entities,
            transcript=transcript,
        ))
        content_ids = [content_saved_only_in_main_database.id, content_saved_in_elastic_search.id, uuid.uuid4()]

        result = self._service.get_content_documents(content_ids)

        self.assertEqual(
            result[1],
            {
                'name': content_saved_only_in_main_database.name,
                'content_type': {
                    'id': str(content_type.id),
                    'name': content_type.name,
                    'image': content_type.image,
                    'image_cover': content_type.image_cover
                },
                'url': re.sub(self.s3_url, settings.AWS_STREAMING_URL, content_saved_only_in_main_database.url),
                'analyzed': False,
                'created_date': str(content_saved_only_in_main_database.created_date),
                'updated_date': str(content_saved_only_in_main_database.updated_date),
                'content_transcript': "",
                'entities': [],
                'language': 'pt',
                'tag': [],
                'summary': "",
                'duration': 0,
                'points': 0
            }
        )
        self.assertEqual(
            result[0],
            {
                'id': content_saved_in_elastic_search.id,
                'name': content_saved_in_elastic_search.name,
                'content_type': {
                    'id': content_type.id,
                    'name': content_type.name,
                    'image': content_type.image,
                    'image_cover': content_type.image_cover
                },
                'category': None,
                'description': None,
                'entities': None,
                'url': re.sub(self.s3_url, settings.AWS_STREAMING_URL, content_saved_in_elastic_search.url),
                'analyzed': False,
                'created_date': content_saved_in_elastic_search.created_date,
                'updated_date': content_saved_in_elastic_search.updated_date,
                'content_transcript': transcript,
                'language': language,
                'tags': tags,
                'summary': summary,
                'duration': 10.0,
                'points': 10.0
            }
        )

    def test_should_get_contents_analyzed_with_default_duration(self):
        self.elastic_search = ElasticSearchStub([])
        content_type = mommy.make(ContentType, extensions='.pdf,.xlsx')
        content_saved_only_in_main_database = mommy.make(LearnContent, content_type=content_type, analyzed=True)
        content_saved_in_elastic_search = mommy.make(LearnContent, content_type=content_type, url="url.com", analyzed=True)

        self._service = ContentService(self.elastic_search, 20, 15)

        tags = [{"text": "texto texto ateoo", "relevance": 0.9900525212287903}]
        summary = "summary"
        language = "pt-BR"
        entities = None
        transcript = "transcript"
        self.elastic_search.save(self._learn_document_to_document(
            content_saved_in_elastic_search,
            tags=tags,
            summary=summary,
            language=language,
            entities=entities,
            transcript=transcript,
            duration=0,
            points=0
        ))
        content_ids = [content_saved_only_in_main_database.id, content_saved_in_elastic_search.id, uuid.uuid4()]

        result = self._service.get_content_documents(content_ids)

        self.assertEqual(result[0]["duration"], self._service.default_content_duration)
        self.assertEqual(result[0]["points"], self._service.default_content_point)
        self.assertEqual(result[1]["duration"], self._service.default_content_duration)
        self.assertEqual(result[1]["points"], self._service.default_content_point)

    def test_should_copy_content(self):
        self.elastic_search = ElasticSearchStub([])
        content_type = mommy.make(ContentType, extensions='.pdf,.xlsx')
        content = mommy.make(LearnContent, content_type=content_type)

        self._service = ContentService(self.elastic_search)
        self.elastic_search.save(self._learn_document_to_document(content))

        copy = self._service.copy(content.id)

        self.assertNotEqual(copy.id, content.id)
        self.assertEqual(copy.content_type.id, content.content_type.id)
        self.assertEqual(copy.url, content.url)
        self.assertEqual(copy.name, content.name)
        self.assertEqual(copy.description, content.description)
        self.assertEqual(copy.analyzed, content.analyzed)
        self.assertEqual(copy.category, content.category)
        self.assertEqual(copy.transcribe_job, content.transcribe_job)

    @mock.patch.object(LearnContentDocument, 'get')
    @mock.patch.object(LearnContentDocument, 'save')
    def test_should_update_content(self, save, get):
        content_type = mommy.make(ContentType, name='SCORM', extensions='html zip')
        mommy.make(ContentPointRule, content_type=content_type, points=100, quantity=1)
        content = mommy.make(LearnContent, content_type=content_type)
        document = self._learn_document_to_document(content)
        get.return_value = document

        self._service = ContentService(self.elastic_search)
        self.elastic_search.save(self._learn_document_to_document(content))

        self._service.update(content.id, {"duration": 100})

        get.assert_called_with(content.id)
        save.assert_called_once()
        self.assertEqual(document.duration, 100)

    @mock.patch.object(ContentService, '_save_file')
    def test_should_create_temp_file_when_format_is_uppercase(self, save_file: mock.MagicMock):
        temp_file = tempfile.NamedTemporaryFile()
        temp_file.name = temp_file.name + '.PNG'
        size = (200, 200)
        color = (255, 0, 0, 0)
        image = Image.new("RGBA", size, color)
        image.save(temp_file, 'PNG')

        self._service.create_temp_file(temp_file)

        save_file.assert_called()
