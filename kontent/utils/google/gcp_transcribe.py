import json
from time import sleep
import cloudconvert
from google.cloud import speech, storage
from config import settings


# pylint: disable=invalid-name
class GoogleTranscribe:

    def __init__(self):
        self.client_speech = speech.SpeechClient()
        self.client_gcs = storage.Client()

        self.flac_extension = "flac"

        self.temp_folder = '{}/temp'.format(settings.BASE_DIR)

        # pylint: disable=consider-using-with
        credencitals_google_file = open(settings.GOOGLE_APPLICATION_CREDENTIALS)  # noqa: SIM115
        self.GOOGLE_CREDENTIALS = json.loads(credencitals_google_file.read())

    def transcribe(self, file_path, vocab_name='pt-BR'):
        filename_without_extension, extension = self._get_filename_and_extension(self._get_filename(file_path)[0])
        blob, gs_url = self.send_file_to_gcs(file_path)

        if extension != self.flac_extension:
            gs_url = self.convert_file_to_flac(blob.name, filename_without_extension, gs_url)

        return self.transcribe_gcs(gs_url, vocab_name)

    def convert_file_to_flac(self, file_path, filename_without_extension, gs_url):
        """
        Convert audio files for flac and save on GCP Storage
        """
        print('Starting conversion...')  # noqa: T201

        job = cloudconvert.Job.create(payload={
            "tasks": {
                'import-file': {
                    'operation': 'import/gcs',
                    "projectid": "keeps-learn-platform",
                    "bucket": settings.GOOGLE_STORAGE_BUCKET,
                    "credentials": self.GOOGLE_CREDENTIALS,
                    "file": file_path,
                },
                'convert-file': {
                    'operation': 'convert',
                    'input': 'import-file',
                    'output_format': 'flac',
                    "audio_codec": "FLAC",
                    "audio_bitrate": 128,
                    "audio_channels": 1,
                    "audio_frequency": 44100,
                    "audio_normalize": None,
                    "strip_metatags": False,
                },
                'export-file': {
                    'operation': 'export/gcs',
                    'input': 'convert-file',
                    "projectid": settings.GOOGLE_CLOUD_PLATFORM_PROJECT_ID,
                    "bucket": settings.GOOGLE_STORAGE_BUCKET,
                    "credentials": self.GOOGLE_CREDENTIALS
                }
            }
        })

        cloudconvert.Job.wait(id=job['id'])

        new_gs_url = gs_url.replace(file_path, filename_without_extension + "." + self.flac_extension)
        print('Finished conversion. New file: {}'.format(new_gs_url))  # noqa: T201

        return new_gs_url

    def send_file_to_gcs(self, file_path, path_gcs=None, bucket_name=settings.GOOGLE_STORAGE_BUCKET):
        print('Starting send to GCS...')  # noqa: T201

        bucket = self.client_gcs.get_bucket(bucket_name)
        blob_name, _ = self._get_filename(file_path, path_gcs)
        blob = bucket.blob(blob_name)
        blob.upload_from_filename(file_path)

        print('Finished send to GCS...')  # noqa: T201

        return blob, 'gs://{bucket}/{path}'.format(bucket=bucket_name, path=blob_name)

    def transcribe_gcs(self, gcs_uri, vocab_name):
        """Asynchronously transcribes the audio file specified by the gcs_uri."""
        audio = speech.RecognitionAudio(uri=gcs_uri)
        config = speech.RecognitionConfig(
            encoding=speech.RecognitionConfig.AudioEncoding.FLAC,
            language_code=vocab_name,
            sample_rate_hertz=44100,
            enable_automatic_punctuation=True
        )

        operation = self.client_speech.long_running_recognize(config, audio)

        print('Starting operation...{}'.format(operation.done()))  # noqa: T201
        while not operation.done():
            sleep(10)
            print('Waiting for operation to complete...{}'.format(operation.done()))  # noqa: T201

        response = operation.result(timeout=20000)
        text_response = ''
        confidence = 0
        count = 0  # noqa: SIM113

        for result in response.results:
            text_response += result.alternatives[0].transcript
            confidence += result.alternatives[0].confidence
            count += 1

        # TODO Implement equal model AWS (utils.aws.aws_transcribe) (get_transcript_text_and_timestamps)
        sentences_and_times = []

        return text_response, sentences_and_times

    @staticmethod
    def _get_filename(file_path, path_join=None):
        paths = file_path.split('/')
        filename = paths[-1] if len(paths) >= 1 else file_path

        if path_join:
            return path_join + filename, filename

        return filename, filename

    @staticmethod
    def _get_filename_and_extension(filename):
        filename_split = filename.split(".")

        if len(filename_split) >= 2:
            return filename_split[-2], filename_split[-1]

        return filename, None
