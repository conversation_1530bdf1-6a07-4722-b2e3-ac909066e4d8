import fitz
import os
import tempfile
import shutil


def compress(input_file: str, output_file: str = None):
    """Compress PDF file"""
    if not output_file:
        output_file = input_file
    
    # Create temporary file
    with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as temp_file:
        temp_path = temp_file.name
    
    try:
        # Open the PDF document
        doc = fitz.open(input_file)
        
        # Create a new PDF with compression applied
        # Use deflate compression and optimize images
        doc.save(temp_path, 
                 garbage=4,  # Remove unused objects
                 deflate=True,  # Use deflate compression
                 clean=True,  # Clean up document structure
                 pretty=False)  # Compact output
        
        doc.close()
        
        # Delete output_file if it exists
        if os.path.exists(output_file):
            os.remove(output_file)
        
        # Copy temporary file to output location
        shutil.move(temp_path, output_file)
        
    finally:
        # Clean up temporary file if it still exists
        if os.path.exists(temp_path):
            os.remove(temp_path)
