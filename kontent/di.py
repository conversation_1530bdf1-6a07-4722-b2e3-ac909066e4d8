import cloudconvert
import injector

from compressors.video_compressor import <PERSON><PERSON><PERSON>pressor
from config import settings
import utils
from constants import KONQUEST_APPLICATION
from custom.discord_webhook import DiscordWebhookLogger
from learn_content.services.check_contents_analyzed_service import CheckContentsAnalyzedService
from learn_content.check_contents_callback import all_contents_analyzed_konquest_callback
from utils.aws import AmazonS3
from utils.google import GoogleTranscribe


class Container:
    _instance = None

    def __new__(cls):
        if not cls._instance:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        cloudconvert.configure(api_key=settings.CLOUD_CONVERT_KEY, sandbox=False)
        self.cloudconvert_client = cloudconvert
        
        self.webhook_logger = DiscordWebhookLogger()

        self._aws_s3_client = utils.aws.AmazonS3(
            access_key=settings.AWS_ACCESS_KEY_ID,
            secret_access=settings.AWS_SECRET_ACCESS_KEY,
            region_name=settings.AWS_REGION_NAME,
            default_bucket=settings.AWS_BUCKET_NAME,
            base_url=settings.AWS_BASE_S3_URL,
            temp_folder=settings.TEMP_UPLOAD_FOLDER
        )

        self.aws_transcribe_client = utils.aws.AmazonTranscribe(access_key=settings.AWS_TRANSCRIBE_ACCESS_KEY,
                                                                secret_access=settings.AWS_TRANSCRIBE_SECRET_KEY,
                                                                region_name=settings.AWS_TRANSCRIBE_REGION,
                                                                s3_client=self._aws_s3_client,
                                                                s3_bucket=settings.AWS_TRANSCRIBE_BUCKET,
                                                                default_vocabulary=settings.AWS_TRANSCRIBE_VOCABULARY,
                                                                webhook_logger=self.webhook_logger,
                                                                cloudconvert_client=self.cloudconvert_client)

        self.aws_comprehend = utils.aws.AwsComprehendClient(aws_access_key=settings.AWS_COMPREHEND_ACCESS_KEY_ID,
                                                            aws_secret_key=settings.AWS_COMPREHEND_SECRET_ACCESS_KEY,
                                                            aws_region=settings.AWS_COMPREHEND_REGION_NAME)

        self.aws_rekognition = utils.aws.AwsRekognitionClient(aws_access_key=settings.AWS_REKOGNITION_ACCESS_KEY_ID,
                                                              aws_secret_key=settings.AWS_REKOGNITION_SECRET_ACCESS_KEY,
                                                              aws_region=settings.AWS_REKOGNITION_REGION_NAME)

        self.summarize = utils.nlp.Summarize()

        self.google_drive_client = utils.google.GoogleDrive(credentials=settings.GOOGLE_APPLICATION_CREDENTIALS,
                                                            base_dir=settings.BASE_DIR,
                                                            webhook_logger=self.webhook_logger)

    @property
    def gcp_transcribe_client(self) -> GoogleTranscribe:
        return utils.google.GoogleTranscribe()

    @staticmethod
    def check_contents_analyzed_service() -> CheckContentsAnalyzedService:
        return CheckContentsAnalyzedService(
            {
                KONQUEST_APPLICATION: all_contents_analyzed_konquest_callback
            }
        )

    def aws_s3_client(self) -> AmazonS3:
        return self._aws_s3_client

    def video_compressor(self) -> VideoCompressor:
        return VideoCompressor(self.aws_s3_client(), settings.VIDEO_COMPRESSOR_SETTINGS)

    def configure(self, binder):
        binder.bind(utils.aws.AmazonS3, to=injector.CallableProvider(self.aws_s3_client))
        binder.bind(utils.aws.AmazonTranscribe, to=injector.CallableProvider(self.aws_transcribe_client))
        binder.bind(utils.aws.AwsComprehendClient, to=injector.CallableProvider(self.aws_comprehend))
        binder.bind(utils.aws.AwsRekognitionClient, to=injector.CallableProvider(self.aws_rekognition))
        binder.bind(utils.google.GoogleDrive, to=injector.CallableProvider(self.google_drive_client))
        binder.bind(utils.nlp.Summarize, to=injector.CallableProvider(self.summarize))