#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Script de debug para verificar o problema com o analisador de blog
"""
import os
import sys
import django

# Configurar Django
sys.path.append('/home/<USER>/keeps/keeps-kontent-server/kontent')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from learn_content.models import ContentType

def debug_content_types():
    """Debug dos ContentTypes disponíveis"""
    print("=== DEBUG CONTENT TYPES ===")
    
    # Listar todos os content types
    content_types = ContentType.objects.all()
    print(f"Total de ContentTypes: {content_types.count()}")
    
    for ct in content_types:
        print(f"- ID: {ct.id}")
        print(f"  Nome: {ct.name}")
        print(f"  Extensions: {ct.extensions}")
        print(f"  Regras de pontos: {ct.contentpointrule_set.count()}")
        if ct.contentpointrule_set.exists():
            rule = ct.contentpointrule_set.first()
            print(f"    -> {rule.quantity} {rule.unit} = {rule.points} pontos")
        print()
    
    # Verificar especificamente o Blog
    print("=== VERIFICANDO BLOG ===")
    blog_ct = ContentType.objects.filter(name='Blog').first()
    if blog_ct:
        print(f"✅ ContentType 'Blog' encontrado: {blog_ct.id}")
        print(f"   Extensions: {blog_ct.extensions}")
        
        rules = blog_ct.contentpointrule_set.first()
        if rules:
            print(f"✅ Regras de pontos encontradas: {rules.quantity} {rules.unit} = {rules.points} pontos")
        else:
            print("❌ Regras de pontos NÃO encontradas")
    else:
        print("❌ ContentType 'Blog' NÃO encontrado")
    
    # Verificar HTML como fallback
    print("=== VERIFICANDO HTML (FALLBACK) ===")
    html_ct = ContentType.objects.filter(name='HTML').first()
    if html_ct:
        print(f"✅ ContentType 'HTML' encontrado: {html_ct.id}")
        print(f"   Extensions: {html_ct.extensions}")
        
        rules = html_ct.contentpointrule_set.first()
        if rules:
            print(f"✅ Regras de pontos encontradas: {rules.quantity} {rules.unit} = {rules.points} pontos")
        else:
            print("❌ Regras de pontos NÃO encontradas")
    else:
        print("❌ ContentType 'HTML' NÃO encontrado")

def test_url2text():
    """Teste da função url2text"""
    print("\n=== TESTE URL2TEXT ===")
    try:
        from analyze.parses.blog import url2text
        
        # Teste com uma URL simples
        test_url = "https://httpbin.org/html"  # URL que retorna HTML simples
        print(f"Testando URL: {test_url}")
        
        text = url2text(test_url)
        print(f"✅ Texto extraído ({len(text)} caracteres)")
        print(f"Primeiros 200 caracteres: {text[:200]}...")
        
    except Exception as e:
        print(f"❌ Erro na função url2text: {e}")

if __name__ == "__main__":
    debug_content_types()
    test_url2text()
