# -*- coding: utf-8 -*-
from injector import inject

from di import Container
from analyze import AnalyzeContent
from analyze.parses.blog import url2text


class AnalyzeBlog(AnalyzeContent):
    """
    Analyzer for blog content from URLs
    """

    @inject
    def __init__(self,
                 container: Container,
                 url: str,
                 points_rules: int,
                 points_quantity: int,
                 workspace_id: str = None) -> None:
        super().__init__(container, workspace_id, points_rules, points_quantity)
        self._url = url

    def execute(self) -> dict:
        """
        Extract text from blog URL and analyze it
        :return dict: analysis results with transcript, duration, points, etc.
        """
        try:
            # Extract text from URL using existing blog parser
            transcript = url2text(self._url)
            
            if not transcript or len(transcript.strip()) == 0:
                # Fallback for empty content
                transcript = "Conteúdo não disponível"
                duration = 60  # 1 minute default
                points = 1
            else:
                # Calculate duration based on text length (reading time)
                duration = self.text_time_duration(transcript)
                # Calculate points based on text content
                points = self.compute_points_by_text(transcript)
            
            # Extract tags, entities and language from text
            keys_tags, entities, language = self.tag(transcript)
            
            # Generate summary
            summarize = self.summary(text=transcript, language=language)

            return {
                "url": self._url,
                "duration": duration,
                "points": points,
                "transcript": transcript,
                "tags": keys_tags,
                "entities": entities,
                "language": language,
                "summary": summarize,
                "analyzed": True
            }
            
        except Exception as error:
            self.logger("AnalyzeBlog", str(error))
            # Return minimal data on error
            return {
                "url": self._url,
                "duration": 60,  # 1 minute default
                "points": 1,
                "transcript": "Erro ao processar conteúdo do blog",
                "tags": [],
                "entities": [],
                "language": "pt",
                "summary": "",
                "analyzed": True
            }
