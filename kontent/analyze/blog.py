# -*- coding: utf-8 -*-
from injector import inject

from di import Container
from analyze import AnalyzeContent
from analyze.parses.blog import url2text


class AnalyzeBlog(AnalyzeContent):
    """
    Analyzer for blog content from URLs
    """

    @inject
    def __init__(self,
                 container: Container,
                 points_rules: int,
                 points_quantity: int,
                 url: str,
                 workspace_id: str = None) -> None:
        super().__init__(container, workspace_id, points_rules, points_quantity)
        self._url = url

    def execute(self) -> dict:
        """
        Extract text from blog URL and analyze it
        :return dict: analysis results with transcript, duration, points, etc.
        """
        transcript = ""
        duration = 60  # Default 1 minute
        points = 1    # Default 1 point
        keys_tags = []
        entities = []
        language = "pt"
        summarize = ""

        try:
            # Extract text from URL using existing blog parser
            transcript = url2text(self._url)

            if transcript and len(transcript.strip()) > 0:
                # Calculate duration based on text length (reading time)
                duration = self.text_time_duration(transcript)
                # Calculate points based on text content
                points = self.compute_points_by_text(transcript)

                # Extract tags, entities and language from text
                try:
                    keys_tags, entities, language = self.tag(transcript)
                except Exception as tag_error:
                    self.logger("AnalyzeBlog - Tag extraction", str(tag_error))
                    keys_tags, entities, language = [], [], "pt"

                # Generate summary
                try:
                    summarize = self.summary(text=transcript, language=language)
                except Exception as summary_error:
                    self.logger("AnalyzeBlog - Summary generation", str(summary_error))
                    summarize = ""
            else:
                transcript = "Conteúdo não disponível para análise"

        except Exception as error:
            self.logger("AnalyzeBlog - URL extraction", str(error))
            transcript = f"Erro ao extrair conteúdo da URL: {str(error)}"

        return {
            "url": self._url,
            "duration": duration,
            "points": points,
            "transcript": transcript,
            "tags": keys_tags,
            "entities": entities,
            "language": language,
            "summary": summarize,
            "analyzed": True
        }
