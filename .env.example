DEBUG=True
ENVIRONMENT=staging
PYTHONUNBUFFERED=1
SECRET_KEY=5i%ul!b#rui^zipl!+u5nh0+7@j4wtu6o\\\\\=!0_99t3axph$@oc\\\\\=

AWS_ACCESS_KEY_ID=AKIAIJ5EAJI7X22H7HNQ
AWS_SECRET_ACCESS_KEY=[aws_secret_key]
AWS_BUCKET_NAME=keeps.kontent.media.hml

AWS_COMPREHEND_ACCESS_KEY_ID=AKIAXKTSYSM6H3H7F7WR
AWS_COMPREHEND_SECRET_ACCESS_KEY=[aws_secret_key]
AWS_REKOGNITION_ACCESS_KEY_ID=AKIAXKTSYSM6N4PXRWFR
AWS_REKOGNITION_SECRET_ACCESS_KEY=[aws_secret_key]
AWS_TRANSCRIBE_ACCESS_KEY=AKIAXKTSYSM6OO6OL3HL
AWS_TRANSCRIBE_BUCKET=keeps.transcribe
AWS_TRANSCRIBE_REGION=us-east-1
AWS_TRANSCRIBE_SECRET_KEY=[aws_secret_key]
AWS_TRANSCRIBE_THRESHOLD=0.5
AWS_TRANSCRIBE_VOCABULARY=keeps_pt_br

CELERY_BROKER_URL=amqp://admin:admin@localhost:5672
CELERY_NAMESPACE=CELERY
CELERY_QUEUE=kontent-stage
# Use RPC backend with RabbitMQ - uses the same RabbitMQ connection for results
CELERY_RESULT_BACKEND=rpc
CELERY_RESULT_SERIALIZER=json
CELERY_TASK_SERIALIZER=json
CELERY_TIMEZONE=UTC

CLOUD_CONVERT_KEY=[secret_key]

DATABASE_HOST=rds-postgres-stage.cpd3dmaosiyq.us-east-1.rds.amazonaws.com
DATABASE_NAME=kontent_dev_db
DATABASE_PASSWORD=[password]
DATABASE_PORT=5432
DATABASE_URL=postgresql://postgres:1234@localhost:5432/kontent_dev_db
DATABASE_USER=postgres

ELASTICSEARCH_HOST=keeps.es.us-east-1.aws.found.io
ELASTICSEARCH_INDEX=kontent-hml
ELASTICSEARCH_PASS=[password]
ELASTICSEARCH_URL=https://keeps.es.us-east-1.aws.found.io
ELASTICSEARCH_USER=elastic

GOOGLE_APPLICATION_CREDENTIALS=config/google_credentials/credential.json
KEEPS_SECRET_TOKEN_INTEGRATION=[secret_token]

KEYCLOAK_CLIENT_ID=kontent
KEYCLOAK_CLIENT_PUBLIC_KEY=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtmbT6XgN9buXZMF6EAjHRNmfGaarVUbik+KtON1YBp0KgTdJn0lCtxhCq5DtvFLOOBrDccw6/RDjeSWlGEcgqggwjPnDIgdtAtgP83mofiILj0mQZfap3o/WknpR6LlveEeM09jnszCTy+WKGZ+HkOJRnhOo/JR7cKSmQxKjEI6NCCNzC4CkwUrPnE5nBhLDk5MvU9KLsBy1TyXoi7c5sh66SLJwD5yn2EzsIxxxje4fYOsXEf6PD6vsAHvjNQNlFELapQTomCECkAwg0fsDZlGAbyK+FoXt/jquKFZ/rcutoXu5tVVNNSfvs5Q4W931KAzqLozd/grR9HlSZAvVWwIDAQAB
KEYCLOAK_CLIENT_SECRET_KEY=[secret_key]
KEYCLOAK_REALM=keeps-dev
KEYCLOAK_SERVER_URL=https://iam.keepsdev.com/auth/

PDFTRON_KEY=demo:1656284562355:7a77bd2f0300000000d2365bcd5c173dcc5f8131ef6fffb2abba5498e0

SCHEDULER_CLEAN_ELASTIC_SEARCH_INDEX=1

SLACK_LOG_CHANNEL_WEBHOOK=*****************************************************************************
